'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePara<PERSON> } from 'next/navigation'
import { useUpdatePackage } from '@/modules/package/mutations/update-package'
import { useGetActivity } from '@/modules/activity/queries/use-get-activity'
import { useGetRegion } from '@/modules/region/queries/use-get-region'
import { IPackage, IPackageHighlights, IPackageDescription, IPackageShortItinerary, IPackageInclusions, IPackageExclusions, IPackageGallery, IPackageYtVideo, IPackageMap, IPackageInfo } from '@/types/package_'
import { Button } from '@/components/ui/button'
import { useGetPackageById } from '@/modules/package/queries/get-package-by-id'
import { PackageDetailsForm } from '@/modules/product/component/package-detail-form'
import { EditTabs } from '@/modules/product/component/edit-tabs'
import { PackageHighlightsForm } from '@/modules/product/component/package-detail/highlight-section'
import { PackageDescriptionForm } from '@/modules/product/component/package-detail/description-section'
import { PackageShortItineraryForm } from '@/modules/product/component/package-detail/short-itinerary-section'
import { PackageInclusionsExclusionsForm } from '@/modules/product/component/package-detail/include-section'
import { PackageGalleryVideoForm } from '@/modules/product/component/package-detail/photo-section'
import { PackageVideoForm } from '@/modules/product/component/package-detail/review-video'
import { PackageMapForm } from '@/modules/product/component/package-detail/map-section'
import { PackageTripInfoForm } from '@/modules/product/component/package-detail/trip-info-section'
import { ZodError } from 'zod'
import { packageSchema } from '@/modules/package/schema/edit-package-schema'


export default function EditPackagePage() {
  const router = useRouter()
  const { id } = useParams() as { id: string }

  const { data: packageResponse, isLoading, isError } = useGetPackageById(id)
  const { data: activityData, isLoading: activityLoading } = useGetActivity()
  const { data: regionData, isLoading: regionLoading } = useGetRegion()

  const updatePackageMutation = useUpdatePackage(id)

  const [packageData, setPackageData] = useState<Partial<IPackage>>({})
  const [highlights, setHighlights] = useState<Partial<IPackageHighlights>>({})
  const [description, setDescription] = useState<Partial<IPackageDescription>>({})
  const [shortItinerary, setShortItinerary] = useState<Partial<IPackageShortItinerary>>({})
  const [inclusions, setInclusions] = useState<Partial<IPackageInclusions>>({})
  const [exclusions, setExclusions] = useState<Partial<IPackageExclusions>>({})
  const [gallery, setGallery] = useState<Partial<IPackageGallery>>({})
  const [ytVideo, setYtVideo] = useState<Partial<IPackageYtVideo>>({})
  const [map, setMap] = useState<Partial<IPackageMap>>({})
  const [info, setInfo] = useState<Partial<IPackageInfo>>({})

  useEffect(() => {
    if (packageResponse?.data) {
      const pkg = packageResponse.data
      setPackageData(pkg)
      setHighlights(pkg.highlights || {})
      setDescription(pkg.description || {})
      setShortItinerary(pkg.shortItinerary || {})
      setInclusions(pkg.inclusions || {})
      setExclusions(pkg.exclusions || {})
      setGallery(pkg.gallery || {})
      setYtVideo(pkg.ytVideo || {})
      setMap(pkg.map || {})
      setInfo(pkg.info || {})
    }
  }, [packageResponse])

  if (isLoading || activityLoading || regionLoading) return <div>Loading...</div>
  if (isError) return <div>Error loading package details.</div>
  if (!packageData) return <div>Loading package data...</div>

  const handleSave = () => {
    if (!packageData) return

    const packageToUpdate: IPackage = {
      ...packageData,
      id: packageData.id || id,
      highlights,
      description,
      shortItinerary,
      inclusions,
      exclusions,
      gallery,
      ytVideo,
      map,
      info,
    } as IPackage

    console.log('Payload being sent:', JSON.stringify(packageToUpdate, null, 2))

    try {
      packageSchema.parse(packageToUpdate);  

      updatePackageMutation.mutate(packageToUpdate, {
        onSuccess: () => {
          alert('Package updated successfully');
          router.push('/package');
        },
        onError: (error: Error) => {
          alert(`Error updating package: ${error.message}`);
        },
      });
    } catch (err) {
      if (err instanceof ZodError) {
        err.issues.forEach(e => alert(e.message)); 
      } else {
        alert("Unexpected validation error");
      }
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <EditTabs packageId={id} />

      <PackageDetailsForm
        formData={packageData}
        onFormDataChange={setPackageData}
        activities={activityData?.data || []}
        regions={regionData?.data || []}
      />

      <PackageHighlightsForm
        highlights={highlights}
        onHighlightsChange={setHighlights}
      />

      <PackageDescriptionForm
        description={description}
        onDescriptionChange={setDescription}
      />

      <PackageShortItineraryForm
        shortItinerary={shortItinerary}
        onShortItineraryChange={setShortItinerary}
      />

      <PackageInclusionsExclusionsForm
        inclusions={inclusions}
        exclusions={exclusions}
        onInclusionsChange={setInclusions}
        onExclusionsChange={setExclusions}
      />

      <PackageGalleryVideoForm
        gallery={gallery}
        ytVideo={ytVideo}
        onGalleryChange={setGallery}
        onYtVideoChange={setYtVideo}
      />

      <PackageVideoForm
        ytVideo={ytVideo}
        onYtVideoChange={setYtVideo}
      />

      <PackageMapForm
        map={map}
        onMapChange={setMap}
      />

      <PackageTripInfoForm
        info={info}
        onInfoChange={setInfo}
      />

      <div className="mt-6 flex justify-end">
        <Button
          onClick={handleSave}
          className="px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
          disabled={updatePackageMutation.isPending}
        >
          {updatePackageMutation.isPending ? 'Updating...' : 'Update Package'}
        </Button>
      </div>
    </div>
  )
}
