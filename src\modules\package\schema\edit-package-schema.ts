import { z } from "zod";

const packageHighlightsSchema = z.object({
    title: z.string().min(1, "Highlights title is required"),
    description: z.string().min(1, "Highlights description is required"),
});

const packageDescriptionSchema = z.object({
    title: z.string().min(1, "Description title is required"),
    description: z.string().min(1, "Description is required"),
});

const shortItinerarySchema = z.object({
    title: z.string().min(1, "Itinerary title is required"),
    points: z
        .array(z.string().min(1, "Point cannot be empty"))
        .min(1, "At least one itinerary point is required"),
});

const inclusionsSchema = z.object({
    title: z.string().min(1, "Inclusions title is required"),
    details: z.string().min(1, "Inclusions details are required"),
});

const exclusionsSchema = z.object({
    title: z.string().min(1, "Exclusions title is required"),
    details: z.string().min(1, "Exclusions details are required"),
});

const packageTripInfoItemSchema = z.object({
  id: z.string().optional(),
  packageInfoId: z.string().optional(),
  title: z.string().min(1, "Trip info item title is required"),
  details: z.string().min(1, "Trip info item details are required"),
  note: z.string().optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

// Trip Info schema
export const packageTripInfoSchema = z.object({
  title: z.string().min(1, "Trip info section title is required"),
  items: z.array(packageTripInfoItemSchema).min(1, "At least one trip info item is required"),
});

// Map schema
export const packageMapSchema = z.object({
  title: z.string().min(1, "Map title is required"),
  map: z.string().url("Map image URL must be valid").optional(),
});

// YouTube Video schema
export const packageYtVideoSchema = z.object({
  title: z.string().min(1, "Video section title is required"),
  links: z.array(z.string().url("Video link must be valid URL")).optional(),
});

// Gallery Image schema
const packageGalleryImageSchema = z.object({
  id: z.string().optional(),
  packageGalleryId: z.string().optional(),
  src: z.string().url("Image src must be a valid URL"),
  caption: z.string().optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

// Gallery schema
export const packageGallerySchema = z.object({
  title: z.string().min(1, "Gallery title is required"),
  PackageGalleryImage: z.array(packageGalleryImageSchema).min(1, "At least one gallery image is required"),
});


// Main package schema including all nested schemas
export const packageSchema = z.object({
    id: z.string().optional(),
    name: z.string().min(1, "Name is required"),
    slug: z.string().min(1, "Slug is required"),
    regionId: z.string().min(1, "Region is required"),
    activityId: z.string().min(1, "Activity is required"),
    accomodation: z.string().min(1, "Accommodation is required"),
    distance: z.string().min(1, "Distance is required"),
    type: z.string().min(1, "Trail Type is required"),
    duration: z.string().min(1, "Duration is required"),
    altitude: z.string().min(1, "Max Altitude is required"),
    meals: z.string().min(1, "Meals Include is required"),
    groupSize: z.string().min(1, "Group Size is required"),
    price: z.string().min(1, "Price is required"),
    discountPrice: z.string().min(1, "Discount Price is required"),
    bestSeason: z.string().min(1, "Best Season is required"),
    transport: z.string().min(1, "Transportation is required"),
    activityPerDay: z.string().min(1, "Activity Per Day is required"),
    grade: z.string().min(1, "Grade is required"),
    bookingLink: z.string().min(1, "Booking Link is required"),
    overviewDescription: z.string().min(1, "Overview Description is required"),
    thumbnail: z.string().min(1, "Thumbnail is required").url("Thumbnail must be a valid URL"),
    mainImage: z.string().min(1, "Main Image is required").url("Main Image must be a valid URL"),
    mainImageAlt: z.string().min(1, "Image Alt Tag is required"),
    pdfBrochure: z.string().optional(),
    published: z.boolean().optional(),
    tripOftheMonth: z.boolean().optional(),
    popularTour: z.boolean().optional(),
    shortTrek: z.boolean().optional(),

    highlights: packageHighlightsSchema.refine(value => value.title && value.description, "Highlights section is required"),
    description: packageDescriptionSchema.refine(value => value.title && value.description, "Description section is required"),
    shortItinerary: shortItinerarySchema.refine(value => value.title && value.points.length > 0, "Itinerary section is required"),
    inclusions: inclusionsSchema.refine(value => value.title && value.details, "Inclusions section is required"),
    exclusions: exclusionsSchema.refine(value => value.title && value.details, "Exclusions section is required"),
    gallery: packageGallerySchema.refine(value => value.title && value.PackageGalleryImage.length > 0, "Gallery section is required"),
    ytVideo: packageYtVideoSchema.refine(value => value.title && value.links.length > 0, "Video section is required"),
    map: packageMapSchema.refine(value => value.title && value.map, "Map section is required"),
    info: packageTripInfoSchema.refine(value => value.title && value.items.length > 0, "Trip Info section is required"),
});
